﻿using System;
using System.Security.Cryptography;
using System.Text;
using TaoBaoWorker.Services.TaoBao.Dtos;


namespace TaoBaoWorker.Services.TaoBao.Impl
{
    public class TaoBaoUtils
    {
        // 存储品牌及其对应的型号列表
        private static readonly Dictionary<string, List<string>> BrandModels = new()
        {
            { "Samsung", ["Galaxy S23", "Galaxy S23 Ultra", "Galaxy Z Fold4", "Galaxy A54", "Galaxy Note 20"] },
            { "Google", ["Pixel 7", "Pixel 7 Pro", "Pixel 6a", "Pixel 6 Pro", "Pixel Fold"] },
            { "Xiaomi", ["13 Pro", "Redmi Note 12", "Poco X5 Pro", "12T Pro", "Civi 2"] },
            { "Huawei", ["P60 Pro", "Mate 50", "Nova 10", "P50 Pocket", "Mate Xs 2"] },
            { "OPPO", ["Find X6 Pro", "Reno8 Pro", "A96", "Find N2 Flip", "OnePlus 11"] },
            { "Vivo", ["X90 Pro", "S16 Pro", "iQOO 11", "Y76s", "NEX 7"] },
            { "Sony", ["Xperia 1 V", "Xperia 5 IV", "Xperia 10 IV", "Xperia Pro-I", "Xperia Ace III"] },
            { "LG", ["Wing 5G", "Velvet", "K92 5G", "Stylo 6", "Q70"] },
            { "Motorola", ["Moto G Power (2023)", "Edge 30 Ultra", "Razr 40 Ultra", "Moto E32", "One 5G Ace"] }
        };


        private static readonly string Base62Chars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        // RC4密钥 
        private const string Rc4Key = "QrMgt8GGYI6T52ZY5AnhtxkLzb8egpFn3j5JELI8H6wtACbUnZ5cc3aYTsTRbmkAkRJeYbtx92LPBWm7nBO9UIl7y5i5MQNmUZNf5QENurR5tGyo7yJ2G0MBjWvy6iAtlAbacKP0SwOUeUWx5dsBdyhxa7Id1APtybSdDgicBDuNjI0mlZFUzZSS9dmN8lBD0WTVOMz0pRZbR3cysomRXOO1ghqjJdTcyDIxzpNAEszN8RMGjrzyU7Hjbmwi6YNK";

        // HMAC密钥原始字节 
        private static readonly byte[] HmacKeyRaw = {
            69, 114, 116, 223, 125, 202, 225, 86, 245, 11, 178, 160, 239, 157, 64, 23,
            161, 18, 174, 192, 113, 116, 240, 153, 49, 226, 9, 217, 33, 176, 188, 178,
            139, 53, 30, 16, 64, 152, 74, 207, 106, 85, 218, 163
        };
        /// <summary>
        /// x-regid 的算法实现
        /// </summary>
        /// <returns></returns>
        public static string GenerateRegId()
        {
            string str = "reg0";

            // 第一部分：编码时间戳相关的随机值
            long timestampValue = Random.Shared.Next(int.MaxValue) + 0x176b986fc01L;
            string part1 = LeftPadding(EncodeBase62(timestampValue), true, 14);

            // 第二部分：编码随机IP地址
            string randomIp = $"{Random.Shared.Next(256)}.{Random.Shared.Next(256)}.{Random.Shared.Next(256)}.{Random.Shared.Next(256)}";
            long ipLong = IpToLong(randomIp);
            string ipEncoded = LeftPadding(EncodeBase62(ipLong), false, 6);
            string part2 = LeftPadding(ipEncoded, true, 12);

            // 第三部分：编码随机值
            long randomValue = Random.Shared.Next(3844);
            string part3 = LeftPadding(EncodeBase62(randomValue), false, 2);

            return $"{str}{part1}{part2}{part3}";
        }
        /// <summary>
        /// 生成UTDID字节数组 (18字节)
        /// </summary>
        public static string GenerateUtdid(string imei)
        {
            using var stream = new MemoryStream();
            // 1. 当前时间戳 (4字节) - Unix时间戳
            int timestamp = (int)(DateTimeOffset.UtcNow.ToUnixTimeSeconds());
            byte[] timestampBytes = IntToBytes(timestamp);
            stream.Write(timestampBytes, 0, 4);

            // 2. 随机数 (4字节)
            int randomInt = Random.Shared.Next();
            byte[] randomBytes = IntToBytes(randomInt);
            stream.Write(randomBytes, 0, 4);

            // 3. 版本号 (1字节) - 固定为3
            stream.WriteByte(3);

            // 4. 保留字节 (1字节) - 固定为0
            stream.WriteByte(0);

            // 5. 设备信息哈希 (4字节)
            // 扩展因子默认为"1" + IMEI (如果IMEI为空则用随机数)
            string extendFactor = "1";
            string deviceStr = extendFactor + (string.IsNullOrEmpty(imei) ? Random.Shared.Next().ToString() : imei);
            int deviceHash = CalculateStringHashCode(deviceStr);
            byte[] deviceHashBytes = IntToBytes(deviceHash);
            stream.Write(deviceHashBytes, 0, 4);

            // 6. 计算前14字节的HMAC并取哈希值 (4字节)
            byte[] first14Bytes = stream.ToArray();
            string hmacResult = CalculateHmac(first14Bytes);
            int hmacHash = CalculateStringHashCode(hmacResult);
            byte[] hmacHashBytes = IntToBytes(hmacHash);
            stream.Write(hmacHashBytes, 0, 4);

            var bytes = stream.ToArray();
            return Convert.ToBase64String(bytes);
        }

        /// <summary>
        /// 随机生成一组品牌和对应的型号
        /// </summary>
        public static BrandAndModelOutput GenerateRandomBrandAndModel()
        {
            BrandAndModelOutput output = new BrandAndModelOutput();
            // 随机选择一个品牌
            var brandList = new List<string>(BrandModels.Keys);
            int randomBrandIndex = Random.Shared.Next(brandList.Count);
            output.Brand = brandList[randomBrandIndex];

            // 从该品牌对应的型号列表中随机选择一个型号
            var modelList = BrandModels[output.Brand];
            int randomModelIndex = Random.Shared.Next(modelList.Count);
            output.Model = modelList[randomModelIndex];
            return output;
        }

        public static string UrlEncode(string str)
        {
            return UrlEncode(str, Encoding.UTF8);
        }
        public static string UrlEncode(string str, Encoding e)
        {

            return Encoding.ASCII.GetString(UrlEncodeToBytes(str, e));
        }
        private static byte[] UrlEncodeToBytes(string str, Encoding e)
        {

            byte[] bytes = e.GetBytes(str);
            return UrlEncodeBytesToBytesInternal(bytes, 0, bytes.Length, false);
        }
        private static byte[] UrlEncodeBytesToBytesInternal(byte[] bytes, int offset, int count, bool alwaysCreateReturnValue)
        {
            int num = 0;
            int num2 = 0;
            for (int i = 0; i < count; i++)
            {
                char ch = (char)bytes[offset + i];
                if (ch == ' ')
                {
                    num++;
                }
                else if (!IsSafe(ch))
                {
                    num2++;
                }
            }
            if (!alwaysCreateReturnValue && num == 0 && num2 == 0)
            {
                return bytes;
            }
            byte[] buffer = new byte[count + num2 * 2];
            int num4 = 0;
            for (int j = 0; j < count; j++)
            {
                byte num6 = bytes[offset + j];
                char ch2 = (char)num6;
                if (IsSafe(ch2))
                {
                    buffer[num4++] = num6;
                }
                else if (ch2 == ' ')
                {
                    buffer[num4++] = 43;
                }
                else
                {
                    buffer[num4++] = 37;
                    buffer[num4++] = (byte)IntToHex(num6 >> 4 & 15);
                    buffer[num4++] = (byte)IntToHex(num6 & 15);
                }
            }
            return buffer;
        }
        private static bool IsSafe(char ch)
        {
            if (ch is >= 'a' and <= 'z' || ch is >= 'A' and <= 'Z' || ch is >= '0' and <= '9')
            {
                return true;
            }
            switch (ch)
            {
                case '\'':
                case '(':
                case ')':
                case '*':
                case '-':
                case '.':
                case '_':
                case '!':
                    return true;
            }
            return false;
        }
        private static char IntToHex(int n)
        {
            if (n <= 9)
            {
                return (char)(n + 48);
            }
            return (char)(n - 10 + 65);
        }

        /// <summary>
        /// 获取时间戳
        /// </summary>
        /// <returns></returns>
        /// <summary>
        /// 整数转字节数组 (大端序)
        /// </summary>
        private static byte[] IntToBytes(int value)
        {
            return
            [
                (byte)((value >> 24) & 0xFF),
                (byte)((value >> 16) & 0xFF),
                (byte)((value >> 8) & 0xFF),
                (byte)(value & 0xFF)
            ];
        }

        /// <summary>
        /// 计算字符串哈希码 (Java String.hashCode()算法)
        /// </summary>
        private static int CalculateStringHashCode(string str)
        {
            if (string.IsNullOrEmpty(str))
                return 0;

            int hash = 0;
            foreach (char c in str)
            {
                hash = hash * 31 + c;
            }
            return hash;
        }

        /// <summary>
        /// 计算HMAC-SHA1
        /// </summary>
        private static string CalculateHmac(byte[] data)
        {
            // 使用RC4解密HMAC密钥
            byte[] decryptedKey = Rc4Decrypt(HmacKeyRaw);

            using var hmac = new HMACSHA1(decryptedKey);
            byte[] hash = hmac.ComputeHash(data);
            return Convert.ToBase64String(hash);
        }

        /// <summary>
        /// RC4解密
        /// </summary>
        private static byte[] Rc4Decrypt(byte[] data)
        {
            return Rc4(data, Rc4Key);
        }

        /// <summary>
        /// RC4算法实现
        /// </summary>
        private  static byte[] Rc4(byte[] data, string key)
        {
            byte[] keyBytes = Encoding.UTF8.GetBytes(key);
            byte[] s = new byte[256];
            byte[] result = new byte[data.Length];

            // 初始化S盒
            for (int i = 0; i < 256; i++)
            {
                s[i] = (byte)i;
            }

            // 密钥调度算法(KSA)
            int j = 0;
            for (int i = 0; i < 256; i++)
            {
                j = (j + s[i] + keyBytes[i % keyBytes.Length]) % 256;
                // 交换s[i]和s[j]
                (s[i], s[j]) = (s[j], s[i]);
            }

            // 伪随机生成算法(PRGA)
            int x = 0, y = 0;
            for (int i = 0; i < data.Length; i++)
            {
                x = (x + 1) % 256;
                y = (y + s[x]) % 256;

                // 交换s[x]和s[y]
                (s[x], s[y]) = (s[y], s[x]);

                // 生成密钥流并异或
                int k = s[(s[x] + s[y]) % 256];
                result[i] = (byte)(data[i] ^ k);
            }

            return result;
        }
        /// <summary>
        /// 左填充字符串
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="useRandomChars">是否使用随机字符填充（true=使用Base62字符，false=使用'0'）</param>
        /// <param name="targetLength">目标长度</param>
        /// <returns>填充后的字符串</returns>
        private static string LeftPadding(string input, bool useRandomChars, int targetLength)
        {
            if (string.IsNullOrEmpty(input))
            {
                input = "";
            }

            int currentLength = input.Length;
            if (currentLength >= targetLength)
            {
                return input;
            }

            int paddingLength = targetLength - currentLength;
            StringBuilder padding = new StringBuilder();

            for (int i = 0; i < paddingLength; i++)
            {
                char paddingChar;
                if (useRandomChars)
                {
                    paddingChar = Base62Chars[Random.Shared.Next(62)];
                }
                else
                {
                    paddingChar = '0';
                }
                padding.Append(paddingChar);
            }

            return padding + input;
        }

        /// <summary>
        /// 将IP地址字符串转换为长整型
        /// </summary>
        /// <param name="ip">IP地址字符串，格式如 "***********"</param>
        /// <returns>IP地址对应的长整型值</returns>
        private static long IpToLong(string ip)
        {
            string[] parts = ip.Split('.');
            if (parts.Length != 4)
            {
                return 0;
            }

            try
            {
                long result = 0;
                result |= (long.Parse(parts[0]) & 255L) << 24;
                result |= (long.Parse(parts[1]) & 255L) << 16;
                result |= (long.Parse(parts[2]) & 255L) << 8;
                result |= (long.Parse(parts[3]) & 255L);

                return result;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// 将长整型编码为Base62字符串（完全模拟sp1.a方法）
        /// </summary>
        /// <param name="value">要编码的长整型值</param>
        /// <returns>Base62编码的字符串</returns>
        private static string EncodeBase62(long value)
        {
            if (value <= 0)
            {
                return "0";
            }

            StringBuilder str = new StringBuilder();

            while (value > 0)
            {
                int remainder = (int)(value % 62);
                str.Append(Base62Chars[remainder]);
                value = value / 62;
            }

            // 反转字符串，完全模拟Java的str.reverse()
            char[] chars = str.ToString().ToCharArray();
            Array.Reverse(chars);
            return new string(chars);
        }
    }
}
