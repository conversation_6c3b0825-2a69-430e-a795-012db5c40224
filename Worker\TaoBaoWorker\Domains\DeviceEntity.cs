﻿using System.ComponentModel.DataAnnotations;

namespace TaoBaoWorker.Domains
{
    public class DeviceEntity
    {
        /// <summary>
        /// 用户ID Cookie中的num
        /// </summary>
        [Key]
        public required long Uid { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public required string Utdid { get; set; }

        /// <summary>
        /// 设备标识，通过API获取
        /// </summary>
        public required string DeviceId { get; set; }

        /// <summary>
        /// 设备品牌
        /// </summary>
        public required string Brand { get; set; }

        /// <summary>
        /// 设备标识
        /// </summary>
        public required string Model { get; set; }

        public required string RegId { get; set; }

        public required long AndroidVer { get; set; }
    }
}
