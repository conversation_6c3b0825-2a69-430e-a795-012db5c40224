﻿namespace TaoBaoWorker.Services.TaoBao.Dtos
{
    public class TaoBaoUserDto
    {
        /// <summary>
        /// 用户ID Cookie中的num
        /// </summary>
        public required string Uid { get; set; }

        /// <summary>
        /// SessionId Cookie中的cookie2
        /// </summary>
        public required string Sid { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Utdid { get; set; }

        /// <summary>
        /// 设备标识，通过API获取
        /// </summary>
        public string? DeviceId { get; set; }

        /// <summary>
        /// 设备品牌
        /// </summary>
        public string Brand { get; set; }

        /// <summary>
        /// 设备标识
        /// </summary>
        public string Model { get; set; }

        public int AndroidVer { get; set; }
        public string RegId { get; set; }
    }
}
