using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using TaoBaoWorker.Common;
using TaoBaoWorker.Services;
using TaoBaoWorker.Services.TaoBao;
using TaoBaoWorker.Services.TaoBao.Dtos;
using TaoBaoWorker.Services.TaoBao.Impl;

namespace TaoBaoWorker
{
    internal class Program
    {
        static async Task Main(string[] args)
        {
       
            var host = Host.CreateDefaultBuilder(args)
                .ConfigureServices((context, services) =>
                {
                    services.AddDbContext<AppDbContext>(options =>
                        options.UseSqlite("Data Source=taobao.db;Cache=Shared;Mode=ReadWriteCreate;Pooling=true;"));
                    // 添加HttpClientFactory
                    services.AddHttpClient();

                    // 注册淘宝相关服务
                    services.AddSingleton<TaoBaoRequest>();
                    services.AddSingleton<ITaoBaoServiceFactory, TaoBaoServiceFactory>();
                    services.AddScoped<DeviceService>();
                    services.AddHttpClient("taobao", client =>
                    {
                        client.DefaultRequestHeaders.Add("x-pv", "6.3");
                        client.DefaultRequestHeaders.Add("x-nettype", "WIFI");
                        client.DefaultRequestHeaders.Add("x-nq", "WIFI");
                        client.DefaultRequestHeaders.Add("x-region-channel", "CN");
                        client.DefaultRequestHeaders.Add("x-features", "27");
                        client.DefaultRequestHeaders.Add("x-app-edition", "ST");
                        client.DefaultRequestHeaders.Add("x-app-conf-v", "0");
                        client.DefaultRequestHeaders.Add("x-bx-version", "6.7.250602");
                        client.DefaultRequestHeaders.Add("f-refer", "mtop");
                        client.DefaultRequestHeaders.Add("x-extdata", "openappkey%3DDEFAULT_AUTH");
                        client.DefaultRequestHeaders.Add("x-ttid", $"600129%40taobao_android_{ConstProp.AppVer}");
                        client.DefaultRequestHeaders.Add("x-app-ver", ConstProp.AppVer);
                        client.DefaultRequestHeaders.Add("a-orange-dq", $"appKey={ConstProp.AppKey}&appVersion={ConstProp.AppVer}&clientAppIndexVersion=1120250801213901061");
                        client.DefaultRequestHeaders.Add("x-appkey", ConstProp.AppKey);
                    });
                })
                .Build();
            var taoBaoFactory = host.Services.GetRequiredService<ITaoBaoServiceFactory>();
            var service = await taoBaoFactory.CreateService(586858678, "10dc45763db899e843aa4503e6e50b45");
            await service.OrderDetailAsync("2847350604164857886");

            // 启动主机
            await host.RunAsync();
        }
    }
}
