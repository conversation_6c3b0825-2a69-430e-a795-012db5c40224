using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using TaoBaoWorker.Extensions;
using TaoBaoWorker.Services;
using TaoBaoWorker.Services.TaoBao;
using TaoBaoWorker.Services.TaoBao.Dtos;

namespace TaoBaoWorker
{
    internal class Program
    {
        static async Task Main(string[] args)
        {
            try
            {
                // 创建主机
                var host = Host.CreateDefaultBuilder(args)
                    .ConfigureServices((context, services) =>
                    {
                        // 添加淘宝服务
                        services.AddTaoBaoServices(context.Configuration);
                    })
                    .Build();

                var logger = host.Services.GetRequiredService<ILogger<Program>>();
                logger.LogInformation("应用程序启动");

                // 初始化数据库
                using (var scope = host.Services.CreateScope())
                {
                    var dbInitializer = scope.ServiceProvider.GetRequiredService<IDatabaseInitializer>();
                    await dbInitializer.InitializeAsync();
                }

                // 使用流畅的API创建淘宝服务
                var serviceBuilder = host.Services.CreateTaoBaoServiceBuilder();

                // 示例：为用户创建服务并获取订单详情
                var taoBaoService = await serviceBuilder
                    .ForUser(586858678, "10dc45763db899e843aa4503e6e50b45")
                    .GetOrCreateAsync();

                logger.LogInformation("开始获取订单详情");
                var orderResult = await taoBaoService.GetOrderDetailAsync("2847350604164857886");

                if (orderResult.Success)
                {
                    logger.LogInformation("订单详情获取成功");
                }
                else
                {
                    logger.LogWarning("订单详情获取失败: {Message}", orderResult.Message);
                }

                // 显示服务统计信息
                logger.LogInformation("当前管理的服务实例数量: {Count}", serviceBuilder.ServiceCount);
                logger.LogInformation("用户ID列表: {UserIds}", string.Join(", ", serviceBuilder.GetAllUserIds()));

                // 启动主机
                await host.RunAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"应用程序启动失败: {ex}");
            }
        }
    }
}
