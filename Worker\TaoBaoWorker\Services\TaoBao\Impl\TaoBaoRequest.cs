using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text;
using TaoBaoWorker.Configuration;
using TaoBaoWorker.Dtos;
using TaoBaoWorker.Services.TaoBao.Dtos;

namespace TaoBaoWorker.Services.TaoBao.Impl
{
    public class TaoBaoRequest
    {
        private readonly ILogger<TaoBaoRequest> _logger;
        private readonly HttpClient _taoBaoHttpClient;
        private readonly HttpClient _httpClient;
        private readonly TaoBaoOptions _options;

        public TaoBaoRequest(
            ILogger<TaoBaoRequest> logger,
            IHttpClientFactory httpClient,
            IOptions<TaoBaoOptions> options)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _taoBaoHttpClient = httpClient.CreateClient("taobao");
            _httpClient = httpClient.CreateClient();
        }

        /// <summary>
        /// 发送GET请求
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <param name="domain">域名</param>
        /// <param name="api">API名称</param>
        /// <param name="data">请求数据</param>
        /// <param name="mapPage">页面信息</param>
        /// <param name="ver">版本号</param>
        /// <returns></returns>
        public async Task<BaseOutput> GetAsync(TaoBaoUserDto user, string domain, string api, string data, MapPageDto mapPage, string ver = "1.0")
        {
            return await SendAsync(user, new SendRequestInput
            {
                Api = api,
                Data = data,
                Domain = domain,
                Headers = null,
                MapPage = mapPage,
                Method = HttpMethod.Get,
                NeedWua = false,
                Version = ver
            });
        }

        /// <summary>
        /// 发送POST请求
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <param name="mapPage">页面信息</param>
        /// <param name="domain">域名</param>
        /// <param name="api">API名称</param>
        /// <param name="data">请求数据</param>
        /// <param name="ver">版本号</param>
        /// <param name="needWua">是否需要WUA</param>
        /// <param name="otherHeaders">其他请求头</param>
        /// <returns></returns>
        public async Task<BaseOutput> PostAsync(TaoBaoUserDto user, string domain, string api, string data, MapPageDto mapPage,
            string ver = "4.0", bool needWua = false, Dictionary<string, string>? otherHeaders = null)
        {
            return await SendAsync(user, new SendRequestInput
            {
                Api = api,
                Data = data,
                Domain = domain,
                Headers = otherHeaders,
                MapPage = mapPage,
                Method = HttpMethod.Post,
                NeedWua = needWua,
                Version = ver
            });
        }
        /// <summary>
        /// 发送请求的主方法
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <param name="input">请求输入参数</param>
        /// <returns>响应结果</returns>
        private async Task<BaseOutput> SendAsync(TaoBaoUserDto user, SendRequestInput input)
        {
            try
            {
                _logger.LogDebug("开始发送请求，API: {Api}, 方法: {Method}, 域名: {Domain}",
                    input.Api, input.Method, input.Domain);

                // 1. 构建签名输入参数
                var signInput = BuildSignInput(user, input);

                // 2. 获取签名
                var signResult = await GetSignAsync(signInput);
                if (signResult == null)
                {
                    _logger.LogWarning("获取签名失败，API: {Api}", input.Api);
                    return new BaseOutput { Code = 1000, Message = "获取签名失败" };
                }

                // 3. 构建请求
                var httpRequest = await BuildHttpRequestAsync(user, input, signInput, signResult);

                // 4. 发送请求
                var response = await SendHttpRequestAsync(httpRequest);

                // 5. 处理响应
                var result = FormatResponse(response);

                _logger.LogDebug("请求完成，API: {Api}, 成功: {Success}", input.Api, result.Success);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送请求异常，API: {Api}, 方法: {Method}", input.Api, input.Method);
                return new BaseOutput { Code = 1001, Message = $"请求异常: {ex.Message}" };
            }
        }

        /// <summary>
        /// 构建签名输入参数
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <param name="input">请求输入</param>
        /// <returns>签名输入参数</returns>
        private TaoBaoSignInput BuildSignInput(TaoBaoUserDto user, SendRequestInput input)
        {
            return new TaoBaoSignInput
            {
                mapPage = input.MapPage,
                map = new MapDto
                {
                    Api = input.Api,
                    DeviceId = user.DeviceId,
                    Sid = user.Sid,
                    Uid = user.Uid,
                    XFeatures = "27",
                    UtDid = user.Utdid,
                    TTid = $"600129@taobao_android_{_options.AppVersion}",
                    T = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(),
                    V = input.Version,
                    Data = input.Data ?? string.Empty
                },
                needWua = input.NeedWua
            };
        }
        /// <summary>
        /// 构建HTTP请求
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <param name="input">请求输入</param>
        /// <param name="signInput">签名输入</param>
        /// <param name="signResult">签名结果</param>
        /// <returns>HTTP请求消息</returns>
        private async Task<HttpRequestMessage> BuildHttpRequestAsync(
            TaoBaoUserDto user,
            SendRequestInput input,
            TaoBaoSignInput signInput,
            TaobaoSignOutput signResult)
        {
            // 构建URL
            var url = BuildRequestUrl(input);

            // 创建请求消息
            var httpRequest = new HttpRequestMessage(input.Method, url);

            // 设置请求体（仅POST请求）
            if (input.Method == HttpMethod.Post)
            {
                httpRequest.Content = BuildRequestContent(input.Data, signResult.wua);
            }

            // 设置请求头
            var headers = BuildRequestHeaders(user, signInput, signResult);
            AddCustomHeaders(headers, input.Headers);
            SetRequestHeaders(httpRequest, headers);

            return httpRequest;
        }

        /// <summary>
        /// 构建请求URL
        /// </summary>
        /// <param name="input">请求输入</param>
        /// <returns>请求URL</returns>
        private string BuildRequestUrl(SendRequestInput input)
        {
            var baseUrl = $"https://{input.Domain}/gw/{input.Api}/{input.Version}/";

            // GET请求需要将数据添加到查询字符串
            if (input.Method == HttpMethod.Get && !string.IsNullOrEmpty(input.Data))
            {
                baseUrl += $"?data={Uri.EscapeDataString(input.Data)}";
            }

            return baseUrl;
        }

        /// <summary>
        /// 构建请求内容（POST请求）
        /// </summary>
        /// <param name="data">请求数据</param>
        /// <param name="wua">WUA参数</param>
        /// <returns>请求内容</returns>
        private StringContent BuildRequestContent(string? data, string? wua)
        {
            object requestModel = string.IsNullOrEmpty(wua)
                ? new { data }
                : new { data, wua };

            var jsonContent = JsonConvert.SerializeObject(requestModel);
            return new StringContent(jsonContent, Encoding.UTF8, "application/json");
        }

        /// <summary>
        /// 构建请求头
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <param name="signInput">签名输入</param>
        /// <param name="signResult">签名结果</param>
        /// <returns>请求头字典</returns>
        private Dictionary<string, string> BuildRequestHeaders(
            TaoBaoUserDto user,
            TaoBaoSignInput signInput,
            TaobaoSignOutput signResult)
        {
            var headers = new Dictionary<string, string>
            {
                ["x-sgext"] = TaoBaoUtils.UrlEncode(signResult.xsgext),
                ["x-sign"] = TaoBaoUtils.UrlEncode(signResult.xsign),
                ["x-sid"] = user.Sid,
                ["x-uid"] = user.Uid,
                ["x-features"] = signInput.map.XFeatures,
                ["x-mini-wua"] = TaoBaoUtils.UrlEncode(signResult.xminiwua),
                ["x-t"] = signInput.map.T,
                ["x-ttid"] = TaoBaoUtils.UrlEncode(signInput.map.TTid),
                ["x-app-ver"] = _options.AppVersion,
                ["x-umt"] = TaoBaoUtils.UrlEncode(signResult.xumt),
                ["x-utdid"] = user.Utdid,
                ["x-appkey"] = signInput.appKey,
                ["x-page-url"] = TaoBaoUtils.UrlEncode(signInput.mapPage.PageId),
                ["x-page-name"] = signInput.mapPage.PageName,
                ["user-agent"] = TaoBaoUtils.UrlEncode($"MTOPSDK/3.1.1.7+(Android;{user.AndroidVer};{user.Brand};{user.Model})+DeviceType(Phone)"),
                ["x-regid"] = user.RegId
            };

            // 添加设备ID（如果存在）
            if (!string.IsNullOrEmpty(user.DeviceId))
            {
                headers["x-devid"] = user.DeviceId;
            }

            return headers;
        }

        /// <summary>
        /// 添加自定义请求头
        /// </summary>
        /// <param name="headers">现有请求头</param>
        /// <param name="customHeaders">自定义请求头</param>
        private void AddCustomHeaders(Dictionary<string, string> headers, Dictionary<string, string>? customHeaders)
        {
            if (customHeaders == null) return;

            foreach (var (key, value) in customHeaders)
            {
                if (!headers.TryAdd(key, value))
                {
                    _logger.LogWarning("自定义请求头 {HeaderKey} 与系统请求头冲突，已忽略", key);
                }
            }
        }

        /// <summary>
        /// 设置HTTP请求头
        /// </summary>
        /// <param name="request">HTTP请求</param>
        /// <param name="headers">请求头字典</param>
        private void SetRequestHeaders(HttpRequestMessage request, Dictionary<string, string> headers)
        {
            foreach (var (key, value) in headers)
            {
                if (!request.Headers.TryAddWithoutValidation(key, value))
                {
                    _logger.LogWarning("无法添加请求头: {HeaderKey} = {HeaderValue}", key, value);
                }
            }
        }

        /// <summary>
        /// 发送HTTP请求
        /// </summary>
        /// <param name="request">HTTP请求</param>
        /// <returns>响应内容</returns>
        private async Task<string> SendHttpRequestAsync(HttpRequestMessage request)
        {
            var response = await _taoBaoHttpClient.SendAsync(request);
            var content = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("HTTP请求失败，状态码: {StatusCode}, 响应: {Response}",
                    response.StatusCode, content);
            }

            return content;
        }

        /// <summary>
        /// 格式化响应结果
        /// </summary>
        /// <param name="response">原始响应</param>
        /// <returns>格式化后的结果</returns>
        private BaseOutput FormatResponse(string response)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(response))
                {
                    _logger.LogWarning("响应内容为空");
                    return new BaseOutput { Code = 1002, Message = "响应内容为空" };
                }

                var json = JObject.Parse(response);

                // 检查ret字段
                if (json.TryGetValue("ret", out var retToken) && retToken is JArray retArray && retArray.Count > 0)
                {
                    var retValue = retArray[0].ToString();

                    if (retValue.StartsWith("SUCCESS::", StringComparison.OrdinalIgnoreCase))
                    {
                        // 成功响应，提取data字段
                        if (json.TryGetValue("data", out var dataToken) && dataToken is JObject dataObject)
                        {
                            _logger.LogDebug("API调用成功，返回数据");
                            return new BaseOutput { Code = 0, Data = dataObject };
                        }

                        _logger.LogDebug("API调用成功，但无数据返回");
                        return new BaseOutput { Code = 0, Data = null };
                    }
                    else
                    {
                        // 业务错误
                        _logger.LogWarning("API调用失败: {ErrorMessage}", retValue);
                        return new BaseOutput { Code = 1, Message = retValue };
                    }
                }

                // 没有ret字段，尝试直接提取data
                if (json.TryGetValue("data", out var directDataToken) && directDataToken is JObject directDataObject)
                {
                    _logger.LogDebug("直接提取到数据字段");
                    return new BaseOutput { Code = 0, Data = directDataObject };
                }

                // 无法识别的响应格式
                _logger.LogError("无法识别的响应格式: {Response}", response);
                return new BaseOutput { Code = 1003, Message = "无法识别的响应格式" };
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "解析响应JSON失败: {Response}", response);
                return new BaseOutput { Code = 1004, Message = "响应格式错误" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理响应时发生异常: {Response}", response);
                return new BaseOutput { Code = 1005, Message = "处理响应异常" };
            }
        }

        /// <summary>
        /// 获取参数签名
        /// </summary>
        /// <param name="signInput">签名输入参数</param>
        /// <returns>签名结果</returns>
        private async Task<TaobaoSignOutput?> GetSignAsync(TaoBaoSignInput signInput)
        {
            const int maxRetries = 3;
            var retryCount = 0;

            while (retryCount < maxRetries)
            {
                try
                {
                    _logger.LogDebug("开始获取签名，尝试次数: {RetryCount}/{MaxRetries}", retryCount + 1, maxRetries);

                    var requestJson = JsonConvert.SerializeObject(signInput, Formatting.None);
                    var content = new StringContent(requestJson, Encoding.UTF8, "application/json");

                    using var response = await _httpClient.PostAsync(_options.SignServerUrl, content);

                    if (response.IsSuccessStatusCode)
                    {
                        var responseContent = await response.Content.ReadAsStringAsync();

                        if (string.IsNullOrWhiteSpace(responseContent))
                        {
                            _logger.LogWarning("签名服务返回空响应");
                            return null;
                        }

                        var signResult = JsonConvert.DeserializeObject<TaobaoSignOutput>(responseContent);

                        if (IsValidSignResult(signResult))
                        {
                            _logger.LogDebug("签名获取成功");
                            return signResult;
                        }
                        else
                        {
                            _logger.LogWarning("签名结果无效: {Response}", responseContent);
                        }
                    }
                    else
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        _logger.LogWarning("签名服务返回错误状态码: {StatusCode}, 内容: {Content}",
                            response.StatusCode, errorContent);
                    }
                }
                catch (HttpRequestException ex)
                {
                    _logger.LogWarning(ex, "签名服务请求异常，尝试次数: {RetryCount}/{MaxRetries}", retryCount + 1, maxRetries);
                }
                catch (JsonException ex)
                {
                    _logger.LogError(ex, "签名响应JSON解析失败");
                    break; // JSON解析错误不需要重试
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取签名时发生未知异常");
                    break; // 未知异常不需要重试
                }

                retryCount++;

                if (retryCount < maxRetries)
                {
                    var delay = TimeSpan.FromMilliseconds(1000 * retryCount); // 递增延迟
                    _logger.LogDebug("等待 {Delay}ms 后重试", delay.TotalMilliseconds);
                    await Task.Delay(delay);
                }
            }

            _logger.LogError("获取签名失败，已达到最大重试次数: {MaxRetries}", maxRetries);
            return null;
        }

        /// <summary>
        /// 验证签名结果是否有效
        /// </summary>
        /// <param name="signResult">签名结果</param>
        /// <returns>是否有效</returns>
        private bool IsValidSignResult(TaobaoSignOutput? signResult)
        {
            return signResult != null &&
                   !string.IsNullOrWhiteSpace(signResult.xsign) &&
                   !string.IsNullOrWhiteSpace(signResult.xsgext) &&
                   !string.IsNullOrWhiteSpace(signResult.xumt) &&
                   !string.IsNullOrWhiteSpace(signResult.xminiwua);
        }
    }
}