﻿using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text;
using TaoBaoWorker.Common;
using TaoBaoWorker.Dtos;
using TaoBaoWorker.Services.TaoBao.Dtos;

namespace TaoBaoWorker.Services.TaoBao.Impl
{
    public class TaoBaoRequest
    {
        private readonly ILogger<TaoBaoRequest> _logger;

        private readonly HttpClient _taoBaoHttpClient;
        private readonly HttpClient _httpClient;
        private const string TaoBaoSignServer = "http://192.168.50.23:8080/secret";
        public TaoBaoRequest(ILogger<TaoBaoRequest> logger, IHttpClientFactory httpClient)
        {
            _logger = logger;
            _taoBaoHttpClient = httpClient.CreateClient("taobao");
            _httpClient = httpClient.CreateClient();
        }

        /// <summary>
        /// 发送GET请求
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <param name="domain">域名</param>
        /// <param name="api">API名称</param>
        /// <param name="data">请求数据</param>
        /// <param name="mapPage">页面信息</param>
        /// <param name="ver">版本号</param>
        /// <returns></returns>
        public async Task<BaseOutput> GetAsync(TaoBaoUserDto user, string domain, string api, string data, MapPageDto mapPage, string ver = "1.0")
        {
            return await SendAsync(user, new SendRequestInput
            {
                Api = api,
                Data = data,
                Domain = domain,
                Headers = null,
                MapPage = mapPage,
                Method = HttpMethod.Get,
                NeedWua = false,
                Version = ver
            });
        }

        /// <summary>
        /// 发送POST请求
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <param name="mapPage">页面信息</param>
        /// <param name="domain">域名</param>
        /// <param name="api">API名称</param>
        /// <param name="data">请求数据</param>
        /// <param name="ver">版本号</param>
        /// <param name="needWua">是否需要WUA</param>
        /// <param name="otherHeaders">其他请求头</param>
        /// <returns></returns>
        public async Task<BaseOutput> PostAsync(TaoBaoUserDto user, string domain, string api, string data, MapPageDto mapPage,
            string ver = "4.0", bool needWua = false, Dictionary<string, string>? otherHeaders = null)
        {
            return await SendAsync(user, new SendRequestInput
            {
                Api = api,
                Data = data,
                Domain = domain,
                Headers = otherHeaders,
                MapPage = mapPage,
                Method = HttpMethod.Post,
                NeedWua = needWua,
                Version = ver
            });
        }
        private async Task<BaseOutput> SendAsync(TaoBaoUserDto user, SendRequestInput input)
        {
            TaoBaoSignInput sign = new TaoBaoSignInput
            {
                mapPage = input.MapPage,
                map = new MapDto
                {
                    Api = input.Api,
                    DeviceId = user.DeviceId ?? null,
                    Sid = user.Sid,
                    Uid = user.Uid,
                    XFeatures = "27",
                    UtDid = user.Utdid, 
                    TTid = $"600129@taobao_android_{ConstProp.AppVer}",
                    T = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(),
                    V = input.Version,
                    Data = input.Data ?? string.Empty
                },
                needWua = input.NeedWua
            };
            var signResult = await GetSign(sign);
            if (signResult == null)
                return new BaseOutput { Code = 1000, Message = "获取签名失败" };

            var headers = new Dictionary<string, string>
            {
                {"x-sgext", TaoBaoUtils.UrlEncode(signResult.xsgext)},
                {"x-sign", TaoBaoUtils.UrlEncode(signResult.xsign)},
                {"x-sid", user.Sid},
                {"x-uid", user.Uid},
                {"x-features", sign.map.XFeatures},
                {"x-mini-wua", TaoBaoUtils.UrlEncode(signResult.xminiwua)},
                {"x-t", sign.map.T},
                {"x-ttid", TaoBaoUtils.UrlEncode(sign.map.TTid)},
                {"x-app-ver", ConstProp.AppVer},
                {"x-umt", TaoBaoUtils.UrlEncode(signResult.xumt)},
                {"x-utdid", user.Utdid},
                {"x-appkey", sign.appKey},
                {"x-page-url", TaoBaoUtils.UrlEncode(sign.mapPage.PageId)},
                {"x-page-name", sign.mapPage.PageName},
                {"user-agent", TaoBaoUtils.UrlEncode($"MTOPSDK/3.1.1.7+(Android;{user.AndroidVer};{user.Brand};{user.Model})+DeviceType(Phone)")},
                {"x-regid", user.RegId},
            };

            if (!string.IsNullOrEmpty(user.DeviceId))
                headers.Add("x-devid", user.DeviceId);
            if (input.Headers != null)
            {
                foreach (var otherHeader in input.Headers)
                {
                    if (!headers.ContainsKey(otherHeader.Key))
                        headers.Add(otherHeader.Key, otherHeader.Value);
                }
            }

            var url = $"https://{input.Domain}/gw/{input.Api}/{input.Version}/";
            if (input.Method == HttpMethod.Get && !string.IsNullOrEmpty(input.Data))
                url += $"?data={Uri.EscapeDataString(input.Data)}";

            var httpMessage = new HttpRequestMessage(input.Method, url);
            if (input.Method == HttpMethod.Post)
            {
                object model;
                if (string.IsNullOrEmpty(signResult.wua))
                    model = new { input.Data };
                else
                    model = new { input.Data, signResult.wua };

                var content = JsonConvert.SerializeObject(model);
                httpMessage.Content = new StringContent(content, Encoding.UTF8, "application/json");
            }

            foreach (var header in headers)
            {
                httpMessage.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }

            var resp = await _taoBaoHttpClient.SendAsync(httpMessage);
            var response = await resp.Content.ReadAsStringAsync();
            return FormatResponse(response);
        }
        private BaseOutput FormatResponse(string response)
        {
            var json = JObject.Parse(response);
            var ret = json["ret"];
            if (ret is { Type: JTokenType.Array })
            {
                var retArr = ret as JArray;
                if (retArr?.Count > 0)
                {
                    var retVal = retArr[0].ToString();
                    //SUCCESS::接口调用成功
                    if (retVal.StartsWith("SUCCESS::"))
                    {
                        if (json.TryGetValue("data", out var data))
                            return new BaseOutput { Code = 0, Data = (JObject)data };
                    }
                    else
                        return new BaseOutput { Code = 1, Message = retVal };
                }

            }
            if (json.TryGetValue("data", out var unexpectedData))
                return new BaseOutput { Code = 0, Data = (JObject)unexpectedData };
            _logger.LogError($"处理响应时出现未知的返回值：{response}");
            return new BaseOutput { Code = 1, Message = "未知的返回值" };
        }

        /// <summary>
        /// 参数签名
        /// </summary>
        /// <param name="postData"></param>
        /// <returns></returns>
        private async Task<TaobaoSignOutput?> GetSign(TaoBaoSignInput postData)
        {
            try
            {
                var json = JsonConvert.SerializeObject(postData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var resp = await _httpClient.PostAsync(TaoBaoSignServer, content);
                if (resp.IsSuccessStatusCode)
                {
                    var response = await resp.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<TaobaoSignOutput>(response);
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "获取参数签名发生异常");
            }

            return null;
        }
    }
}