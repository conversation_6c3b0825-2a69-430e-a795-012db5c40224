using System.ComponentModel.DataAnnotations;

namespace TaoBaoWorker.Configuration
{
    /// <summary>
    /// 淘宝服务配置选项
    /// </summary>
    public class TaoBaoOptions
    {
        public const string SectionName = "TaoBao";

        /// <summary>
        /// 应用版本
        /// </summary>
        [Required]
        public string AppVersion { get; set; } = "10.29.10";

        /// <summary>
        /// 应用密钥
        /// </summary>
        [Required]
        public string AppKey { get; set; } = "21646297";

        /// <summary>
        /// 签名服务器地址
        /// </summary>
        [Required]
        public string SignServerUrl { get; set; } = "http://192.168.50.23:8080/secret";

        /// <summary>
        /// 请求超时时间（秒）
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryCount { get; set; } = 3;

        /// <summary>
        /// 默认请求头配置
        /// </summary>
        public Dictionary<string, string> DefaultHeaders { get; set; } = new()
        {
            ["x-pv"] = "6.3",
            ["x-nettype"] = "WIFI",
            ["x-nq"] = "WIFI",
            ["x-region-channel"] = "CN",
            ["x-features"] = "27",
            ["x-app-edition"] = "ST",
            ["x-app-conf-v"] = "0",
            ["x-bx-version"] = "6.7.250602",
            ["f-refer"] = "mtop",
            ["x-extdata"] = "openappkey%3DDEFAULT_AUTH"
        };
    }

    /// <summary>
    /// 数据库配置选项
    /// </summary>
    public class DatabaseOptions
    {
        public const string SectionName = "Database";

        /// <summary>
        /// 连接字符串
        /// </summary>
        [Required]
        public string ConnectionString { get; set; } = "Data Source=taobao.db;Cache=Shared;Mode=ReadWriteCreate;Pooling=true;";

        /// <summary>
        /// 是否启用敏感数据日志
        /// </summary>
        public bool EnableSensitiveDataLogging { get; set; } = false;

        /// <summary>
        /// 是否启用详细错误
        /// </summary>
        public bool EnableDetailedErrors { get; set; } = false;
    }
}
