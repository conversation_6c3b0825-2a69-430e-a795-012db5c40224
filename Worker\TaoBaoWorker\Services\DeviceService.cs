﻿using Microsoft.EntityFrameworkCore;
using TaoBaoWorker.Common;
using TaoBaoWorker.Domains;

namespace TaoBaoWorker.Services
{
    public class DeviceService
    {
        private readonly AppDbContext _db;

        public DeviceService(AppDbContext db)
        {
            _db = db;
        }

        /// <summary>
        /// 添加设备信息
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task AddAsync(DeviceEntity entity)
        {
            _db.Devices.Add(entity);
            await _db.SaveChangesAsync();
        }

        /// <summary>
        /// 获取设备信息
        /// </summary>
        /// <param name="uid"></param>
        /// <returns></returns>
        public async Task<DeviceEntity?> GetAsync(long uid)
        {
            return await _db.Devices.FirstOrDefaultAsync(i => i.Uid == uid);
        }
    }
}
