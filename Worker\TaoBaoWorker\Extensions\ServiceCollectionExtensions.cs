using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using TaoBaoWorker.Common;
using TaoBaoWorker.Configuration;
using TaoBaoWorker.Services;
using TaoBaoWorker.Services.TaoBao;
using TaoBaoWorker.Services.TaoBao.Impl;

namespace TaoBaoWorker.Extensions
{
    /// <summary>
    /// 服务集合扩展方法
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 添加淘宝服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configuration">配置</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddTaoBaoServices(this IServiceCollection services, IConfiguration configuration)
        {
            // 配置选项
            services.Configure<TaoBaoOptions>(configuration.GetSection(TaoBaoOptions.SectionName));
            services.Configure<DatabaseOptions>(configuration.GetSection(DatabaseOptions.SectionName));

            // 验证配置
            services.AddSingleton<IValidateOptions<TaoBaoOptions>, ValidateOptionsService<TaoBaoOptions>>();
            services.AddSingleton<IValidateOptions<DatabaseOptions>, ValidateOptionsService<DatabaseOptions>>();

            // 数据库上下文
            services.AddDbContext<AppDbContext>((serviceProvider, options) =>
            {
                var dbOptions = serviceProvider.GetRequiredService<IOptions<DatabaseOptions>>().Value;
                options.UseSqlite(dbOptions.ConnectionString);
                
                if (dbOptions.EnableSensitiveDataLogging)
                    options.EnableSensitiveDataLogging();
                    
                if (dbOptions.EnableDetailedErrors)
                    options.EnableDetailedErrors();
            });

            // 数据库初始化服务
            services.AddScoped<IDatabaseInitializer, DatabaseInitializer>();

            // HTTP客户端
            services.AddHttpClient();
            services.AddHttpClient("taobao", (serviceProvider, client) =>
            {
                var taoBaoOptions = serviceProvider.GetRequiredService<IOptions<TaoBaoOptions>>().Value;
                
                // 设置超时
                client.Timeout = TimeSpan.FromSeconds(taoBaoOptions.TimeoutSeconds);
                
                // 添加默认请求头
                foreach (var header in taoBaoOptions.DefaultHeaders)
                {
                    client.DefaultRequestHeaders.Add(header.Key, header.Value);
                }
                
                // 添加动态请求头
                client.DefaultRequestHeaders.Add("x-ttid", $"600129%40taobao_android_{taoBaoOptions.AppVersion}");
                client.DefaultRequestHeaders.Add("x-app-ver", taoBaoOptions.AppVersion);
                client.DefaultRequestHeaders.Add("a-orange-dq", 
                    $"appKey={taoBaoOptions.AppKey}&appVersion={taoBaoOptions.AppVersion}&clientAppIndexVersion=1120250801213901061");
                client.DefaultRequestHeaders.Add("x-appkey", taoBaoOptions.AppKey);
            });

            // 业务服务
            services.AddScoped<DeviceService>();
            services.AddSingleton<TaoBaoRequest>();
            services.AddSingleton<ITaoBaoServiceFactory, TaoBaoServiceFactory>();

            return services;
        }


    }

    /// <summary>
    /// 配置验证服务
    /// </summary>
    /// <typeparam name="T">配置类型</typeparam>
    public class ValidateOptionsService<T> : IValidateOptions<T> where T : class
    {
        public ValidateOptionsResult Validate(string? name, T options)
        {
            var validationResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();
            var context = new System.ComponentModel.DataAnnotations.ValidationContext(options);
            
            if (!System.ComponentModel.DataAnnotations.Validator.TryValidateObject(options, context, validationResults, true))
            {
                var errors = validationResults.Select(r => r.ErrorMessage).Where(e => !string.IsNullOrEmpty(e));
                return ValidateOptionsResult.Fail(errors!);
            }

            return ValidateOptionsResult.Success;
        }
    }
}
