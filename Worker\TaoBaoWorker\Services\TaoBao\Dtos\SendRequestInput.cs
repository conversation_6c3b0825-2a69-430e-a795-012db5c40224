﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TaoBaoWorker.Services.TaoBao.Dtos
{
    internal class SendRequestInput
    {
        public required HttpMethod Method { get; set; }
        public required string Domain { get; set; }
        public required string Api { get; set; }
        public string? Data { get; set; }
        public required MapPageDto MapPage { get; set; }
        public string Version { get; set; } = "1.0";
        public bool NeedWua { get; set; } = false;
        public Dictionary<string, string>? Headers { get; set; }
    }
}
