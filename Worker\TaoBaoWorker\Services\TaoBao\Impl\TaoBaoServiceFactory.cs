using System.Collections.Concurrent;
using TaoBaoWorker.Domains;
using TaoBaoWorker.Services.TaoBao.Dtos;

namespace TaoBaoWorker.Services.TaoBao.Impl
{
    /// <summary>
    /// 淘宝服务工厂，用于管理多个用户的 TaoBaoService 实例
    /// </summary>
    public class TaoBaoServiceFactory : ITaoBaoServiceFactory
    {
        private readonly TaoBaoRequest _taoBaoRequest;
        private readonly DeviceService _deviceService;
        private readonly ConcurrentDictionary<long, TaoBaoService> _services;

        public TaoBaoServiceFactory(TaoBaoRequest taoBaoRequest, DeviceService deviceService)
        {
            _taoBaoRequest = taoBaoRequest ?? throw new ArgumentNullException(nameof(taoBaoRequest));
            _deviceService = deviceService;
            _services = new ConcurrentDictionary<long, TaoBaoService>();
        }

        /// <summary>
        /// 获取或创建指定用户的 TaoBaoService 实例
        /// </summary>
        /// <param name="uid">用户信息</param>
        /// <param name="sessionId">用户信息</param>
        /// <returns>TaoBaoService 实例</returns> 
        public async Task<TaoBaoService> CreateService(long uid, string sessionId)
        {

            var device = await _deviceService.GetAsync(uid);
            var userDto = new TaoBaoUserDto
            {
                Uid = uid.ToString(),
                Sid = sessionId
            };
            TaoBaoService service;
            if (device != null)
            {
                userDto.Brand = device.Brand;
                userDto.Model = device.Model;
                userDto.Utdid = device.Utdid;
                userDto.RegId = device.RegId;
                service = new TaoBaoService(_taoBaoRequest, userDto);
            }
            else
            {
                var brandAndModel = TaoBaoUtils.GenerateRandomBrandAndModel();
                service = new TaoBaoService(_taoBaoRequest, userDto);
                userDto.Brand = brandAndModel.Brand;
                userDto.Model = brandAndModel.Model;
                userDto.Utdid = TaoBaoUtils.GenerateUtdid(string.Empty);
                userDto.RegId = TaoBaoUtils.GenerateRegId();
                userDto.AndroidVer = Random.Shared.Next(7, 13);
                var deviceId = await service.GetDeviceIdAsync();
                await _deviceService.AddAsync(new DeviceEntity
                {
                    Brand = userDto.Brand,
                    Model = userDto.Model,
                    Uid = uid,
                    Utdid = userDto.Utdid,
                    RegId = userDto.RegId,
                    AndroidVer = userDto.AndroidVer,
                    DeviceId = deviceId ?? string.Empty
                });
            }
            _services.AddOrUpdate(uid, service, (_, _) => service);
            return service;
        }

        /// <summary>
        /// 移除指定用户的服务实例
        /// </summary>
        /// <param name="uid">用户 UID</param>
        /// <returns>是否成功移除</returns>
        public TaoBaoService? GetService(long uid)
        {
            return _services.GetValueOrDefault(uid);
        }

        /// <summary>
        /// 获取当前管理的服务实例数量
        /// </summary>
        public int ServiceCount => _services.Count;

        /// <summary>
        /// 获取所有已创建的服务实例的用户 UID
        /// </summary>
        /// <returns>用户 UID 列表</returns>
        public IEnumerable<long> GetAllUserIds()
        {
            return _services.Keys.ToList();
        }

        /// <summary>
        /// 清除所有服务实例
        /// </summary>
        public void ClearAll()
        {
            _services.Clear();
        }
    }
}
