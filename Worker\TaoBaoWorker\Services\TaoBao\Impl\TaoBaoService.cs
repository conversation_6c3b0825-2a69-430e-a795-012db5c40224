﻿using TaoBaoWorker.Dtos;
using TaoBaoWorker.Services.TaoBao.Dtos;

namespace TaoBaoWorker.Services.TaoBao.Impl
{
    /// <summary>
    /// 淘宝服务类，每个用户账号对应一个实例
    /// </summary>
    public class TaoBaoService
    {
        private readonly TaoBaoRequest _request;
        private readonly TaoBaoUserDto _user;
        /// <summary>
        /// 构造函数，绑定特定用户
        /// </summary>
        /// <param name="request">淘宝请求服务</param>
        /// <param name="user">用户信息</param>
        public TaoBaoService(TaoBaoRequest request, TaoBaoUserDto user)
        {
            _request = request ?? throw new ArgumentNullException(nameof(request));
            _user = user ?? throw new ArgumentNullException(nameof(user));
        }

        /// <summary>
        /// 获取订单详情
        /// </summary>
        /// <param name="orderId">订单ID</param>
        /// <returns></returns>
        public async Task OrderDetailAsync(string orderId)
        {
            var data = $"{{\"appName\":\"tborder\",\"appVersion\":\"3.0\",\"archive\":\"false\",\"bizOrderId\":\"{orderId}\",\"extParams\":\"{{\\\"weexPage\\\":\\\"neworderdetail4\\\",\\\"openFrom\\\":\\\"taoBoughtList\\\",\\\"installApp\\\":\\\"ALIPAY\\\"}}\",\"from\":\"OrderListActivity\",\"useV2\":\"true\"}}";
            var detail = await GetAsync("trade-acs.m.taobao.com", "mtop.taobao.order.query.detailv2", data, new MapPageDto("http://h5.m.taobao.com/awp/base/order/listultron.htm", "com.taobao.tao.welcome.Welcome"));

        }

        /// <summary>
        /// 获取deviceId
        /// </summary>
        /// <param name="input">设备的基础信息</param>
        /// <returns></returns>
        public async Task<string?> GetDeviceIdAsync()
        {
            //{"new_device":"true","c3":"input.IMSI","c4":"","c5":"input.SerialNumber","c6":"input.AndroidId","device_global_id":"aI3BpY0FwD8DADxlftr6O4Dn","c0":"SAMSUNG","c1":"AOSP on Intel Platform","c2":"input.IMEI"}
            var data = $"{{\"new_device\":\"true\",\"c3\":\"\",\"c4\":\"\",\"c5\":\"\",\"c6\":\"\",\"device_global_id\":\"{_user.Utdid}\",\"c0\":\"{_user.Brand}\",\"c1\":\"{_user.Model}\",\"c2\":\"\"}}";
            var resp = await GetAsync("guide-acs.m.taobao.com", "mtop.sys.newdeviceid", data, new MapPageDto("http://m.taobao.com/index.htm", "com.taobao.tao.TBMainActivity"), "4.0");
            if (!resp.Success) return string.Empty;
            var deviceId = resp.Data?["device_id"]?.ToString();
            if (!string.IsNullOrEmpty(deviceId))
                _user.DeviceId = deviceId;
            return deviceId;
        }



        /// <summary>
        /// 发送GET请求
        /// </summary>
        /// <param name="domain">域名</param>
        /// <param name="api">API名称</param>
        /// <param name="data">请求数据</param>
        /// <param name="mapPage">页面信息</param>
        /// <param name="ver">版本号</param>
        /// <returns></returns>
        private async Task<BaseOutput> GetAsync(string domain, string api, string data, MapPageDto mapPage, string ver = "1.0")
        {
            return await _request.GetAsync(_user, domain, api, data, mapPage, ver);
        }

        /// <summary>
        /// 发送POST请求
        /// </summary>
        /// <param name="domain">域名</param>
        /// <param name="api">API名称</param>
        /// <param name="data">请求数据</param>
        /// <param name="mapPage">页面信息</param>
        /// <param name="ver">版本号</param>
        /// <param name="needWua">是否需要WUA</param>
        /// <param name="otherHeaders">其他请求头</param>
        /// <returns></returns>
        private async Task<BaseOutput> PostAsync(
            string domain,
            string api,
            string data,
            MapPageDto mapPage,
            string ver = "4.0",
            bool needWua = false,
            Dictionary<string, string>? otherHeaders = null)
        {
            return await _request.PostAsync(_user, domain, api, data, mapPage, ver, needWua, otherHeaders);
        }
    }
}
