using TaoBaoWorker.Services.TaoBao.Dtos;
using TaoBaoWorker.Services.TaoBao.Impl;

namespace TaoBaoWorker.Services.TaoBao
{
    /// <summary>
    /// 淘宝服务工厂接口
    /// </summary>
    public interface ITaoBaoServiceFactory
    {
        Task<TaoBaoService> CreateService(long uid, string sessionId);
        /// <summary>
        /// 获取或创建指定用户的 TaoBaoService 实例
        /// </summary>
        /// <param name="uid">用户ID</param>
        /// <returns>TaoBaoService 实例</returns>
        TaoBaoService? GetService(long uid);


        /// <summary>
        /// 获取当前管理的服务实例数量
        /// </summary>
        int ServiceCount { get; }

        /// <summary>
        /// 获取所有已创建的服务实例的用户 UID
        /// </summary>
        /// <returns>用户 UID 列表</returns>
        IEnumerable<long> GetAllUserIds();

        /// <summary>
        /// 清除所有服务实例
        /// </summary>
        void ClearAll();
    }
}
